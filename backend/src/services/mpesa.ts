import axios from 'axios';
import { logger } from '@/utils/logger';
import crypto from 'crypto';

export interface MpesaConfig {
  consumerKey: string;
  consumerSecret: string;
  environment: 'sandbox' | 'production';
  passkey: string;
  shortcode: string;
  callbackUrl: string;
}

export interface MpesaPaymentRequest {
  amount: number;
  phoneNumber: string;
  accountReference: string;
  transactionDesc: string;
}

export interface MpesaPaymentResponse {
  merchantRequestId: string;
  checkoutRequestId: string;
  responseCode: string;
  responseDescription: string;
  customerMessage: string;
}

export interface MpesaCallbackData {
  Body: {
    stkCallback: {
      MerchantRequestID: string;
      CheckoutRequestID: string;
      ResultCode: number;
      ResultDesc: string;
      CallbackMetadata?: {
        Item: Array<{
          Name: string;
          Value: string | number;
        }>;
      };
    };
  };
}

export interface MpesaTransactionDetails {
  amount: number;
  mpesaReceiptNumber: string;
  transactionDate: string;
  phoneNumber: string;
}

export interface C2BPaymentRequest {
  shortCode: string;
  commandID: string;
  amount: number;
  msisdn: string;
  billRefNumber: string;
}

export interface B2CPaymentRequest {
  amount: number;
  phoneNumber: string;
  commandID: 'SalaryPayment' | 'BusinessPayment' | 'PromotionPayment';
  remarks: string;
  occasion?: string;
}

export class MpesaService {
  private config: MpesaConfig;
  private baseUrl: string;
  private accessToken?: string;
  private tokenExpiry?: Date;

  constructor() {
    this.config = {
      consumerKey: process.env['MPESA_CONSUMER_KEY'] || '',
      consumerSecret: process.env['MPESA_CONSUMER_SECRET'] || '',
      environment: (process.env['MPESA_ENVIRONMENT'] as 'sandbox' | 'production') || 'sandbox',
      passkey: process.env['MPESA_PASSKEY'] || '',
      shortcode: process.env['MPESA_SHORTCODE'] || '',
      callbackUrl: process.env['MPESA_CALLBACK_URL'] || '',
    };

    this.baseUrl = this.config.environment === 'sandbox'
      ? 'https://sandbox.safaricom.co.ke'
      : 'https://api.safaricom.co.ke';

    // Use demo credentials if not configured for sandbox testing
    if (this.config.environment === 'sandbox' && (!this.config.consumerKey || !this.config.passkey || !this.config.shortcode)) {
      this.setupDemoCredentials();
    }

    this.validateConfig();
  }

  /**
   * Setup demo credentials for sandbox testing
   */
  private setupDemoCredentials(): void {
    logger.info('Setting up M-Pesa demo credentials for sandbox testing');

    this.config = {
      consumerKey: 'bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919',
      consumerSecret: 'A49c25EWeCHkDMfgDY9A9zQP8hhmcC2sIBwA16uuaRJxtYzWVdFUlxtntLWgZeLU',
      shortcode: '174379',
      passkey: 'bfb279f9aa9bdbcf158e97dd71a467cd2e0c893059b10f78e6b72ada1ed2c919',
      callbackUrl: process.env['MPESA_CALLBACK_URL'] || 'http://localhost:5000/api/token-purchase/callback',
      environment: 'sandbox'
    };
  }

  private validateConfig(): void {
    const requiredFields = ['consumerKey', 'consumerSecret', 'passkey', 'shortcode', 'callbackUrl'];
    const missingFields = requiredFields.filter(field => !this.config[field as keyof MpesaConfig]);

    if (missingFields.length > 0) {
      // In development, just log a warning instead of throwing an error
      if (process.env['NODE_ENV'] === 'development') {
        logger.warn(`Warning: Missing M-Pesa configuration: ${missingFields.join(', ')}. M-Pesa functionality will be disabled.`);
        return;
      }
      throw new Error(`Missing required M-Pesa configuration: ${missingFields.join(', ')}`);
    }

    // Log successful configuration for debugging
    logger.info('M-Pesa configuration validated successfully', {
      environment: this.config.environment,
      shortcode: this.config.shortcode,
      hasConsumerKey: !!this.config.consumerKey,
      hasConsumerSecret: !!this.config.consumerSecret,
      hasPasskey: !!this.config.passkey,
      callbackUrl: this.config.callbackUrl
    });
  }

  /**
   * Mock STK Push for development/testing
   */
  private mockSTKPush(paymentRequest: MpesaPaymentRequest): MpesaPaymentResponse {
    const formattedPhone = this.formatPhoneNumber(paymentRequest.phoneNumber);
    const mockMerchantRequestId = `mock_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const mockCheckoutRequestId = `ws_CO_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    logger.info('Mock M-Pesa STK Push initiated', {
      merchantRequestId: mockMerchantRequestId,
      checkoutRequestId: mockCheckoutRequestId,
      amount: paymentRequest.amount,
      phoneNumber: formattedPhone,
      note: 'This is a mock payment for development/testing'
    });

    // Simulate successful response
    return {
      merchantRequestId: mockMerchantRequestId,
      checkoutRequestId: mockCheckoutRequestId,
      responseCode: '0',
      responseDescription: 'Success. Request accepted for processing',
      customerMessage: 'Success. Request accepted for processing (MOCK)',
    };
  }

  private formatPhoneNumber(phoneNumber: string): string {
    // Remove any non-digit characters
    let cleaned = phoneNumber.replace(/\D/g, '');

    // Handle different formats
    if (cleaned.startsWith('0')) {
      cleaned = '254' + cleaned.substring(1);
    } else if (cleaned.startsWith('+254')) {
      cleaned = cleaned.substring(1);
    } else if (!cleaned.startsWith('254')) {
      cleaned = '254' + cleaned;
    }

    return cleaned;
  }

  private generateTimestamp(): string {
    return new Date().toISOString().replace(/[^0-9]/g, '').slice(0, -3);
  }

  private generatePassword(timestamp: string): string {
    return Buffer.from(`${this.config.shortcode}${this.config.passkey}${timestamp}`).toString('base64');
  }

  private async getAccessToken(): Promise<string> {
    try {
      // Check if we have a valid token
      if (this.accessToken && this.tokenExpiry && new Date() < this.tokenExpiry) {
        return this.accessToken;
      }

      const auth = Buffer.from(`${this.config.consumerKey}:${this.config.consumerSecret}`).toString('base64');

      // Debug logging
      logger.info('M-Pesa access token request details:', {
        url: `${this.baseUrl}/oauth/v1/generate?grant_type=client_credentials`,
        consumerKeyLength: this.config.consumerKey.length,
        consumerSecretLength: this.config.consumerSecret.length,
        authHeaderLength: auth.length
      });

      const response = await axios.get(`${this.baseUrl}/oauth/v1/generate?grant_type=client_credentials`, {
        headers: {
          'Authorization': `Basic ${auth}`,
        },
      });

      this.accessToken = response.data.access_token;
      // Token expires in 1 hour, set expiry to 55 minutes from now
      this.tokenExpiry = new Date(Date.now() + 55 * 60 * 1000);

      logger.info('M-Pesa access token obtained successfully');
      return this.accessToken;

    } catch (error: any) {
      logger.error('Failed to get M-Pesa access token:', {
        message: error.message || 'Unknown error',
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        requestUrl: `${this.baseUrl}/oauth/v1/generate?grant_type=client_credentials`,
        consumerKeyLength: this.config.consumerKey.length,
        consumerSecretLength: this.config.consumerSecret.length
      });
      throw new Error('Failed to authenticate with M-Pesa API');
    }
  }

  async initiateSTKPush(paymentRequest: MpesaPaymentRequest): Promise<MpesaPaymentResponse> {
    try {
      // Validate input
      if (!paymentRequest.amount || paymentRequest.amount <= 0) {
        throw new Error('Invalid payment amount. Amount must be greater than 0');
      }

      if (paymentRequest.amount < 1) {
        throw new Error('Minimum payment amount is KES 1');
      }

      if (paymentRequest.amount > 70000) {
        throw new Error('Maximum payment amount is KES 70,000');
      }

      if (!paymentRequest.phoneNumber) {
        throw new Error('Phone number is required');
      }

      // Validate phone number format
      const phoneRegex = /^(254[17]\d{8}|0[17]\d{8})$/;
      if (!phoneRegex.test(paymentRequest.phoneNumber.replace(/\D/g, ''))) {
        throw new Error('Invalid Kenyan phone number format');
      }

      // Check if mock payments are enabled
      if (process.env['MOCK_MPESA_PAYMENTS'] === 'true') {
        return this.mockSTKPush(paymentRequest);
      }

      const accessToken = await this.getAccessToken();
      const timestamp = this.generateTimestamp();
      const password = this.generatePassword(timestamp);
      const formattedPhone = this.formatPhoneNumber(paymentRequest.phoneNumber);

      const requestBody = {
        BusinessShortCode: this.config.shortcode,
        Password: password,
        Timestamp: timestamp,
        TransactionType: 'CustomerPayBillOnline',
        Amount: Math.round(paymentRequest.amount), // Ensure integer amount
        PartyA: formattedPhone,
        PartyB: this.config.shortcode,
        PhoneNumber: formattedPhone,
        CallBackURL: this.config.callbackUrl,
        AccountReference: paymentRequest.accountReference || 'PEDI_PAYMENT',
        TransactionDesc: paymentRequest.transactionDesc || 'Pedi Platform Payment',
      };

      const response = await axios.post(
        `${this.baseUrl}/mpesa/stkpush/v1/processrequest`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000, // 30 second timeout
        }
      );

      // Validate response structure
      if (!response.data) {
        throw new Error('Invalid response from M-Pesa API');
      }

      // Check for error response
      if (response.data.ResponseCode && response.data.ResponseCode !== '0') {
        throw new Error(response.data.ResponseDescription || 'M-Pesa request failed');
      }

      // Validate required response fields
      if (!response.data.MerchantRequestID || !response.data.CheckoutRequestID) {
        throw new Error('Invalid response: Missing required fields');
      }

      logger.info('M-Pesa STK Push initiated successfully', {
        merchantRequestId: response.data.MerchantRequestID,
        checkoutRequestId: response.data.CheckoutRequestID,
        amount: paymentRequest.amount,
        phoneNumber: formattedPhone,
        responseCode: response.data.ResponseCode
      });

      return {
        merchantRequestId: response.data.MerchantRequestID,
        checkoutRequestId: response.data.CheckoutRequestID,
        responseCode: response.data.ResponseCode || '0',
        responseDescription: response.data.ResponseDescription || 'Request processed successfully',
        customerMessage: response.data.CustomerMessage || 'Please check your phone for M-Pesa prompt',
      };

    } catch (error: any) {
      // Safely extract error information without circular references
      const errorInfo = {
        message: error.message || 'Unknown error',
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        amount: paymentRequest.amount,
        phoneNumber: paymentRequest.phoneNumber,
      };

      logger.error('Failed to initiate M-Pesa STK Push:', errorInfo);

      if (error.response?.data?.errorMessage) {
        throw new Error(`M-Pesa Error: ${error.response.data.errorMessage}`);
      }

      if (error.response?.data?.ResponseDescription) {
        throw new Error(`M-Pesa Error: ${error.response.data.ResponseDescription}`);
      }

      throw new Error('Failed to initiate payment. Please try again.');
    }
  }

  async querySTKPushStatus(checkoutRequestId: string): Promise<any> {
    try {
      if (!checkoutRequestId) {
        throw new Error('Checkout request ID is required');
      }

      const accessToken = await this.getAccessToken();
      const timestamp = this.generateTimestamp();
      const password = this.generatePassword(timestamp);

      const requestBody = {
        BusinessShortCode: this.config.shortcode,
        Password: password,
        Timestamp: timestamp,
        CheckoutRequestID: checkoutRequestId,
      };

      const response = await axios.post(
        `${this.baseUrl}/mpesa/stkpushquery/v1/query`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000,
        }
      );

      logger.info('M-Pesa STK Push status queried successfully', {
        checkoutRequestId,
        resultCode: response.data.ResultCode,
        resultDesc: response.data.ResultDesc,
      });

      return response.data;

    } catch (error: any) {
      logger.error('Failed to query M-Pesa STK Push status:', {
        error: error.message,
        checkoutRequestId,
        response: error.response?.data,
      });
      throw new Error('Failed to query payment status');
    }
  }

  /**
   * Process M-Pesa STK Push callback
   */
  processSTKCallback(callbackData: MpesaCallbackData): {
    success: boolean;
    transactionDetails?: MpesaTransactionDetails;
    error?: string;
  } {
    try {
      const { stkCallback } = callbackData.Body;

      logger.info('Processing M-Pesa STK callback', {
        merchantRequestId: stkCallback.MerchantRequestID,
        checkoutRequestId: stkCallback.CheckoutRequestID,
        resultCode: stkCallback.ResultCode,
      });

      // Check if payment was successful
      if (stkCallback.ResultCode === 0) {
        // Payment successful - extract transaction details
        const metadata = stkCallback.CallbackMetadata?.Item || [];

        const getMetadataValue = (name: string) => {
          const item = metadata.find(item => item.Name === name);
          return item ? item.Value : null;
        };

        const transactionDetails: MpesaTransactionDetails = {
          amount: Number(getMetadataValue('Amount')) || 0,
          mpesaReceiptNumber: String(getMetadataValue('MpesaReceiptNumber')) || '',
          transactionDate: String(getMetadataValue('TransactionDate')) || '',
          phoneNumber: String(getMetadataValue('PhoneNumber')) || '',
        };

        logger.info('M-Pesa payment successful', {
          merchantRequestId: stkCallback.MerchantRequestID,
          checkoutRequestId: stkCallback.CheckoutRequestID,
          transactionDetails,
        });

        return {
          success: true,
          transactionDetails,
        };
      } else {
        // Payment failed
        logger.warn('M-Pesa payment failed', {
          merchantRequestId: stkCallback.MerchantRequestID,
          checkoutRequestId: stkCallback.CheckoutRequestID,
          resultCode: stkCallback.ResultCode,
          resultDesc: stkCallback.ResultDesc,
        });

        return {
          success: false,
          error: stkCallback.ResultDesc || 'Payment failed',
        };
      }
    } catch (error: any) {
      logger.error('Error processing M-Pesa callback:', error);
      return {
        success: false,
        error: 'Failed to process payment callback',
      };
    }
  }

  /**
   * Initiate B2C payment (Business to Customer)
   */
  async initiateB2CPayment(paymentRequest: B2CPaymentRequest): Promise<any> {
    try {
      const accessToken = await this.getAccessToken();
      const formattedPhone = this.formatPhoneNumber(paymentRequest.phoneNumber);

      const requestBody = {
        InitiatorName: process.env['MPESA_INITIATOR_NAME'] || '',
        SecurityCredential: process.env['MPESA_SECURITY_CREDENTIAL'] || '',
        CommandID: paymentRequest.commandID,
        Amount: Math.round(paymentRequest.amount),
        PartyA: this.config.shortcode,
        PartyB: formattedPhone,
        Remarks: paymentRequest.remarks,
        QueueTimeOutURL: `${this.config.callbackUrl}/b2c/timeout`,
        ResultURL: `${this.config.callbackUrl}/b2c/result`,
        Occasion: paymentRequest.occasion || '',
      };

      const response = await axios.post(
        `${this.baseUrl}/mpesa/b2c/v1/paymentrequest`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000,
        }
      );

      logger.info('B2C payment initiated successfully', {
        conversationId: response.data.ConversationID,
        originatorConversationId: response.data.OriginatorConversationID,
        amount: paymentRequest.amount,
        phoneNumber: formattedPhone,
      });

      return response.data;

    } catch (error: any) {
      logger.error('Failed to initiate B2C payment:', {
        error: error.message,
        response: error.response?.data,
        amount: paymentRequest.amount,
        phoneNumber: paymentRequest.phoneNumber,
      });
      throw new Error('Failed to initiate B2C payment');
    }
  }

  /**
   * Register C2B URLs
   */
  async registerC2BUrls(): Promise<any> {
    try {
      const accessToken = await this.getAccessToken();

      const requestBody = {
        ShortCode: this.config.shortcode,
        ResponseType: 'Completed',
        ConfirmationURL: `${this.config.callbackUrl}/c2b/confirmation`,
        ValidationURL: `${this.config.callbackUrl}/c2b/validation`,
      };

      const response = await axios.post(
        `${this.baseUrl}/mpesa/c2b/v1/registerurl`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000,
        }
      );

      logger.info('C2B URLs registered successfully', response.data);
      return response.data;

    } catch (error: any) {
      logger.error('Failed to register C2B URLs:', {
        error: error.message,
        response: error.response?.data,
      });
      throw new Error('Failed to register C2B URLs');
    }
  }

  /**
   * Validate callback signature (for security)
   */
  validateCallbackSignature(payload: string, signature: string): boolean {
    try {
      const expectedSignature = crypto
        .createHmac('sha256', process.env['MPESA_CALLBACK_SECRET'] || '')
        .update(payload)
        .digest('hex');

      return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );
    } catch (error) {
      logger.error('Error validating callback signature:', error);
      return false;
    }
  }
}

// Export the class for instantiation when needed

// Create a lazy-loaded instance
let mpesaServiceInstance: MpesaService | null = null;

export const getMpesaService = (): MpesaService => {
  if (!mpesaServiceInstance) {
    logger.info('Initializing M-Pesa service...');
    mpesaServiceInstance = new MpesaService();
    logger.info('M-Pesa service initialized successfully');
  }
  return mpesaServiceInstance;
};
